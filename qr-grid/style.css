:root {
  --col-width: 200px;
  --row-height: 240px;
}

body {
  margin: 0;
  padding: 20px;
  background: #f0f2f5;
}

.grid-container {
  display: grid;
  gap: 8px; /* 减小间距以利用更多空间 */
  justify-content: center;
  transition: grid-template-columns 0.3s ease;
  max-height: calc(100vh - 40px);
  overflow: hidden;
  padding: 10px; /* 统一内边距 */
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px; /* 减小内边距 */
  background: white;
  border-radius: 6px; /* 减小圆角 */
  box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* 更轻的阴影 */
  transition: transform 0.2s;
}

.grid-item:hover {
  transform: translateY(-5px);
}

.qr-code {
  width: 100%;
  height: 80%;
  margin-bottom: 10px;
}

.item-text {
  font-family: 'Helvetica Neue', sans-serif;
  font-size: 14px;
  color: #333;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}