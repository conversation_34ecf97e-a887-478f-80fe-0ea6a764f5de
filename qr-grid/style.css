body {
  margin: 0;
  padding: 0;
  background: #f8f8f8;
  font-family: Arial, sans-serif;
  overflow: hidden; /* 确保不出现滚动条 */
  height: 100vh;
  width: 100vw;
  position: fixed; /* 固定位置，防止在移动设备上出现弹性滚动 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-container {
  display: grid;
  gap: 1px;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  overflow: hidden; /* 确保网格容器内容不溢出 */
  position: absolute; /* 绝对定位以确保完全填充视口 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
  background: white;
  border-radius: 1px;
  box-shadow: none;
  box-sizing: border-box; /* 确保 padding 包含在元素尺寸内 */
  height: 100%;
}

.grid-item:hover {
  transform: none;
}

.qr-code {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  padding: 2px 0;
}

.qr-code svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  display: block; /* 防止底部间隙 */
}

.item-text {
  font-size: 10px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  width: 100%;
  flex-shrink: 0;
  min-height: 12px;
  padding: 2px 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}