body {
  margin: 0;
  padding: 5px;
  background: #f5f5f5;
  font-family: Arial, sans-serif;
  overflow: hidden; /* 防止页面滚动 */
}

.grid-container {
  display: grid;
  gap: 4px;
  justify-content: center;
  padding: 2px;
  box-sizing: border-box;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08);
  box-sizing: border-box;
  height: 100%;
}

.grid-item:hover {
  transform: scale(1.02);
  transition: transform 0.15s;
}

.qr-code {
  width: 100%;
  flex: 1;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px; /* 减小最小高度，适应更紧凑的布局 */
}

.qr-code svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.item-text {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.3;
  width: 100%;
  flex-shrink: 0; /* 防止文本被压缩 */
  min-height: 16px; /* 确保文本有最小高度 */
}