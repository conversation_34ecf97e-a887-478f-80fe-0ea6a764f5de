body {
  margin: 0;
  padding: 0;
  background: #f8f8f8;
  font-family: Arial, sans-serif;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

.grid-container {
  display: grid;
  gap: 1px;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
  background: white;
  border-radius: 1px;
  box-shadow: none;
  box-sizing: border-box;
  height: 100%;
}

.grid-item:hover {
  transform: none;
}

.qr-code {
  width: 100%;
  flex: 1;
  margin-bottom: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.qr-code svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.item-text {
  font-size: 9px;
  color: #333;
  text-align: center;
  line-height: 1;
  width: 100%;
  flex-shrink: 0;
  min-height: 10px;
  padding: 0;
  margin: 0;
}