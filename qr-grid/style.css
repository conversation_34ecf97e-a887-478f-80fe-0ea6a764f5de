body {
  margin: 0;
  padding: 5px;
  background: #f5f5f5;
  font-family: Arial, sans-serif;
}

.grid-container {
  display: grid;
  gap: 4px;
  justify-content: center;
  max-height: 100vh;
  overflow: hidden;
  padding: 2px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08);
}

.grid-item:hover {
  transform: scale(1.02);
  transition: transform 0.15s;
}

.qr-code {
  width: 100%;
  flex: 1;
  margin-bottom: 4px;
}

.qr-code svg {
  width: 100%;
  height: 100%;
}

.item-text {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}