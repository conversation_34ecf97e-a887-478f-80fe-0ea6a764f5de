body {
  margin: 0;
  padding: 0;
  background: #f8f8f8;
  font-family: Arial;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.grid-container {
  display: grid;
  gap: 1px;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
  background: #fff;
  border-radius: 1px;
  box-sizing: border-box;
  height: 100%;
}
.qr-code {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  padding: 2px 0;
}
.qr-code svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  display: block;
}
.item-text {
  font-size: 10px;
  color: #333;
  text-align: center;
  line-height: 1.2;
  width: 100%;
  flex-shrink: 0;
  min-height: 12px;
  padding: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.item-url {
  font-size: 6px!important;
  color: #666;
  text-align: center;
  line-height: 1;
  width: 100%;
  flex-shrink: 0;
  min-height: 10px;
  padding: 1px;
  word-break: break-all;
  max-height: 20px;
  overflow: hidden;
}
