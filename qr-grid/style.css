body {
  margin: 0;
  padding: 2px;
  background: #f8f8f8;
  font-family: Arial, sans-serif;
  overflow: hidden;
}

.grid-container {
  display: grid;
  gap: 2px;
  justify-content: center;
  padding: 1px;
  box-sizing: border-box;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 3px;
  background: white;
  border-radius: 2px;
  box-shadow: 0 1px 1px rgba(0,0,0,0.05);
  box-sizing: border-box;
  height: 100%;
}

.grid-item:hover {
  transform: scale(1.01);
  transition: transform 0.1s;
}

.qr-code {
  width: 100%;
  flex: 1;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.qr-code svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.item-text {
  font-size: 10px;
  color: #333;
  text-align: center;
  line-height: 1.1;
  width: 100%;
  flex-shrink: 0;
  min-height: 12px;
  padding: 0;
  margin: 0;
}