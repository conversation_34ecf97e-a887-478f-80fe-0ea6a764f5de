function calculateGrid() {
  const MIN_CELL_WIDTH = 120; // 进一步减小最小宽度
  const MIN_CELL_HEIGHT = 140; // 进一步减小最小高度

  const container = document.querySelector('.grid-container');
  const viewportWidth = window.innerWidth - 4; // 减小边距
  const viewportHeight = window.innerHeight - 4;

  // 计算列数
  const columns = Math.max(1, Math.floor(viewportWidth / MIN_CELL_WIDTH));
  const gapTotal = (columns + 1) * 2; // 2px gap
  const cellWidth = Math.max(MIN_CELL_WIDTH, (viewportWidth - gapTotal) / columns);

  // 计算单元格高度，更紧凑的比例
  const cellHeight = Math.max(MIN_CELL_HEIGHT, cellWidth * 1.05);

  // 计算可以完整显示的行数
  const maxRows = Math.max(1, Math.floor(viewportHeight / (cellHeight + 2))); // 2px gap

  // 计算总的可显示项目数
  const maxItems = columns * maxRows;

  container.style.gridTemplateColumns = `repeat(${columns}, ${cellWidth}px)`;
  container.style.gridAutoRows = `${cellHeight}px`;

  return { columns, maxRows, maxItems, cellHeight };
}

function generateQRItems() {
  const container = document.querySelector('.grid-container');
  container.innerHTML = '';

  // 计算网格布局
  const gridInfo = calculateGrid();

  // 根据实际可显示数量生成数据
  const itemCount = Math.min(gridInfo.maxItems, 50); // 最多50个，避免生成过多
  const demoData = Array(itemCount).fill().map((_,i) => ({
    text: `QR ${i+1}`,
    url: `https://example.com/qr-${i+1}`
  }));

  demoData.forEach(data => {
    const div = document.createElement('div');
    div.className = 'grid-item';

    const qrDiv = document.createElement('div');
    qrDiv.className = 'qr-code';

    const qr = qrcode(0, 'L'); // 使用低纠错级别，生成更小的二维码
    qr.addData(data.url);
    qr.make();
    qrDiv.innerHTML = qr.createSvgTag({ cellSize: 2, margin: 0 }); // 更小的单元格和无边距

    const textDiv = document.createElement('div');
    textDiv.className = 'item-text';
    textDiv.textContent = data.text;

    div.appendChild(qrDiv);
    div.appendChild(textDiv);
    container.appendChild(div);
  });

  console.log(`视口尺寸: ${window.innerWidth}×${window.innerHeight}`);
  console.log(`网格布局: ${gridInfo.columns} 列 × ${gridInfo.maxRows} 行`);
  console.log(`单元格尺寸: ${Math.round((window.innerWidth - 4 - (gridInfo.columns + 1) * 2) / gridInfo.columns)}×${Math.round(gridInfo.cellHeight)}`);
  console.log(`显示 ${itemCount} 个二维码`);
}

let resizeTimer;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    generateQRItems(); // generateQRItems 内部会调用 calculateGrid
  }, 150);
});

window.addEventListener('DOMContentLoaded', () => {
  generateQRItems();
});