function calculateGrid(){
  const c=document.querySelector('.grid-container'),
  w=c.clientWidth-4,h=c.clientHeight-4,
  minW=140,minH=180;
  let cols=Math.max(1,Math.floor(w/minW)),
  cellW=(w-(cols-1))/cols,
  cellH=Math.max(minH,cellW*1.2),
  rows=Math.max(1,Math.floor(h/(cellH+1))),
  actualH=(h-(rows-1))/rows;
  c.style.gridTemplateColumns=`repeat(${cols},${cellW}px)`;
  c.style.gridAutoRows=`${actualH}px`;
  return{columns:cols,maxRows:rows,maxItems:cols*rows,cellHeight:actualH};
}

function generateRandomString(length){
  const chars='0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result='';
  for(let i=0;i<length;i++){
    result+=chars.charAt(Math.floor(Math.random()*chars.length));
  }
  return result;
}

function generateURL(barcode){
  const prefix='HTTPS://Y2WM.CN/99.1000.1/AC20';
  const middle=barcode.substring(7,12);
  const randomSuffix=String(Math.floor(Math.random()*4)).padStart(3,'0');
  const randomChars=generateRandomString(21);
  return prefix+middle+randomSuffix+randomChars;
}

let cigaretteData=[];

async function loadCigaretteData(){
  try{
    const response=await fetch('fujian_cigarettes_data.json');
    const data=await response.json();
    cigaretteData=data.filter(item=>item.烟型==='烤烟型');
    console.log(`加载了${cigaretteData.length}条烤烟型数据`);
    generateQRItems();
  }catch(error){
    console.error('加载数据失败:',error);
    generateQRItems();
  }
}

function generateQRItems(){
  const c=document.querySelector('.grid-container');
  c.innerHTML='';
  const g=calculateGrid(),n=g.maxItems;

  for(let i=0;i<n;i++){
    const d=document.createElement('div'),q=document.createElement('div'),t=document.createElement('div'),u=document.createElement('div');
    d.className='grid-item';
    q.className='qr-code';
    t.className='item-text';
    u.className='item-url';
    u.style.cssText='font-size:8px;color:#666;margin-top:2px;word-break:break-all;line-height:1;max-height:20px;overflow:hidden;';

    let brandName,url;
    if(cigaretteData.length>0){
      const item=cigaretteData[i%cigaretteData.length];
      brandName=item.品牌规格名称;
      url=generateURL(item.条包条形码);
    }else{
      brandName=`烤烟型 ${i+1}`;
      url=`HTTPS://Y2WM.CN/99.1000.1/AC20${String(i).padStart(5,'0')}${String(Math.floor(Math.random()*4)).padStart(3,'0')}${generateRandomString(21)}`;
    }

    const qr=qrcode(0,'L');
    qr.addData(url);
    qr.make();
    q.innerHTML=qr.createSvgTag({cellSize:3,margin:1});
    t.textContent=brandName;
    u.textContent=url;

    d.appendChild(q);
    d.appendChild(t);
    d.appendChild(u);
    c.appendChild(d);
  }
  console.log(`${g.columns}×${g.maxRows}=${n}个二维码`);
}

let t;
addEventListener('resize',()=>{clearTimeout(t);t=setTimeout(generateQRItems,150)});
addEventListener('DOMContentLoaded',loadCigaretteData);