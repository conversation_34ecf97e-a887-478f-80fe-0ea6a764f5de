function calculateGrid(){
  const c=document.querySelector('.grid-container'),
  w=c.clientWidth-4,h=c.clientHeight-4,
  minW=140,minH=180;
  let cols=Math.max(1,Math.floor(w/minW)),
  cellW=(w-(cols-1))/cols,
  cellH=Math.max(minH,cellW*1.2),
  rows=Math.max(1,Math.floor(h/(cellH+1))),
  actualH=(h-(rows-1))/rows;
  c.style.gridTemplateColumns=`repeat(${cols},${cellW}px)`;
  c.style.gridAutoRows=`${actualH}px`;
  return{columns:cols,maxRows:rows,maxItems:cols*rows,cellHeight:actualH};
}

function getBrowserFingerprint(){
  const canvas=document.createElement('canvas');
  const ctx=canvas.getContext('2d');
  ctx.textBaseline='top';
  ctx.font='14px Arial';
  ctx.fillText('Browser fingerprint',2,2);
  const canvasData=canvas.toDataURL();

  const fingerprint=[
    navigator.userAgent,
    navigator.language,
    screen.width+'x'+screen.height,
    new Date().getTimezoneOffset(),
    canvasData.slice(-50),
    navigator.hardwareConcurrency||4,
    navigator.deviceMemory||4
  ].join('|');

  let hash=0;
  for(let i=0;i<fingerprint.length;i++){
    const char=fingerprint.charCodeAt(i);
    hash=((hash<<5)-hash)+char;
    hash=hash&hash;
  }
  return Math.abs(hash);
}

function seededRandom(seed){
  const x=Math.sin(seed)*10000;
  return x-Math.floor(x);
}

function generateRandomString(length,seed){
  const chars='0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result='';
  for(let i=0;i<length;i++){
    const randomIndex=Math.floor(seededRandom(seed+i)*chars.length);
    result+=chars.charAt(randomIndex);
  }
  return result;
}

function generateURL(barcode,timestamp,fingerprint,index){
  const prefix='HTTPS://Y2WM.CN/99.1000.1/AC20';
  const middle=barcode.substring(7,12);
  const seed=timestamp+fingerprint+index;
  const randomSuffix=String(Math.floor(seededRandom(seed)*4)).padStart(3,'0');
  const randomChars=generateRandomString(21,seed+1000);
  return prefix+middle+randomSuffix+randomChars;
}

let cigaretteData=[];
let browserFingerprint=getBrowserFingerprint();
let updateTimer;

function waitForQRCode(callback){
  if(typeof QRious!=='undefined'){
    callback();
  }else{
    setTimeout(()=>waitForQRCode(callback),100);
  }
}

async function loadCigaretteData(){
  try{
    const response=await fetch('fujian_cigarettes_data.json');
    const data=await response.json();
    cigaretteData=data.filter(item=>item.烟型==='烤烟型');
    console.log(`加载了${cigaretteData.length}条烤烟型数据`);
    console.log(`浏览器指纹: ${browserFingerprint}`);
    waitForQRCode(startAutoUpdate);
  }catch(error){
    console.error('加载数据失败:',error);
    waitForQRCode(startAutoUpdate);
  }
}

function startAutoUpdate(){
  generateQRItems();
  updateTimer=setInterval(generateQRItems,1000);
}

function generateQRItems(){
  const c=document.querySelector('.grid-container');
  const g=calculateGrid(),n=g.maxItems;
  const timestamp=Math.floor(Date.now()/1000);

  if(c.children.length!==n){
    c.innerHTML='';
    for(let i=0;i<n;i++){
      const d=document.createElement('div'),q=document.createElement('div'),t=document.createElement('div'),u=document.createElement('div');
      d.className='grid-item';
      q.className='qr-code';
      t.className='item-text';
      u.className='item-url';
      u.style.cssText='font-size:8px;color:#666;margin-top:2px;word-break:break-all;line-height:1;max-height:20px;overflow:hidden;';
      d.appendChild(q);
      d.appendChild(t);
      d.appendChild(u);
      c.appendChild(d);
    }
  }

  for(let i=0;i<n;i++){
    const d=c.children[i];
    const q=d.querySelector('.qr-code');
    const t=d.querySelector('.item-text');
    const u=d.querySelector('.item-url');

    let brandName,url;
    if(cigaretteData.length>0){
      const item=cigaretteData[i%cigaretteData.length];
      brandName=item.品牌规格名称;
      url=generateURL(item.条包条形码,timestamp,browserFingerprint,i);
    }else{
      brandName=`烤烟型 ${i+1}`;
      const seed=timestamp+browserFingerprint+i;
      url=`HTTPS://Y2WM.CN/99.1000.1/AC20${String(i).padStart(5,'0')}${String(Math.floor(seededRandom(seed)*4)).padStart(3,'0')}${generateRandomString(21,seed)}`;
    }

    try{
      const qr=new QRious({
        value:url,
        size:120,
        level:'L'
      });
      q.innerHTML=`<img src="${qr.toDataURL()}" style="width:100%;height:auto;">`;
    }catch(error){
      console.error('二维码生成失败:',error);
      q.innerHTML=`<div style="width:100%;height:100px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;font-size:10px;">生成失败</div>`;
    }

    t.textContent=brandName;
    u.textContent=url;
  }
  console.log(`${timestamp}: ${g.columns}×${g.maxRows}=${n}个二维码已更新`);
}

let t;
addEventListener('resize',()=>{
  clearTimeout(t);
  t=setTimeout(()=>{
    clearInterval(updateTimer);
    startAutoUpdate();
  },150);
});
addEventListener('DOMContentLoaded',loadCigaretteData);
addEventListener('visibilitychange',()=>{
  if(document.hidden){
    clearInterval(updateTimer);
  }else{
    startAutoUpdate();
  }
});