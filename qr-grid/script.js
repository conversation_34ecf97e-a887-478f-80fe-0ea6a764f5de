function calculateGrid() {
  const MIN_CELL_WIDTH = 140; // 增加最小宽度以适应更大的二维码
  const MIN_CELL_HEIGHT = 160; // 增加最小高度以适应更大的二维码

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // 计算列数
  const columns = Math.max(1, Math.floor(viewportWidth / MIN_CELL_WIDTH));
  const gapTotal = (columns - 1) * 1; // 1px gap between columns
  const cellWidth = Math.max(MIN_CELL_WIDTH, (viewportWidth - gapTotal) / columns);

  // 计算单元格高度，给二维码更多空间
  const cellHeight = Math.max(MIN_CELL_HEIGHT, cellWidth * 1.15);

  // 计算可以完整显示的行数，确保不超出视口
  const maxRows = Math.max(1, Math.floor(viewportHeight / (cellHeight + 1))); // 1px gap between rows

  // 重新计算实际单元格高度，确保完美填充视口
  const actualCellHeight = (viewportHeight - (maxRows - 1) * 1) / maxRows;

  // 计算总的可显示项目数
  const maxItems = columns * maxRows;

  const container = document.querySelector('.grid-container');
  container.style.gridTemplateColumns = `repeat(${columns}, ${cellWidth}px)`;
  container.style.gridAutoRows = `${actualCellHeight}px`;

  return { columns, maxRows, maxItems, cellHeight: actualCellHeight };
}

function generateQRItems() {
  const container = document.querySelector('.grid-container');
  container.innerHTML = '';

  // 计算网格布局
  const gridInfo = calculateGrid();

  // 精确生成一屏能显示的数量
  const itemCount = gridInfo.maxItems;
  const demoData = Array(itemCount).fill().map((_,i) => ({
    text: `QR ${i+1}`,
    url: `https://example.com/qr-${i+1}`
  }));

  demoData.forEach(data => {
    const div = document.createElement('div');
    div.className = 'grid-item';

    const qrDiv = document.createElement('div');
    qrDiv.className = 'qr-code';

    const qr = qrcode(0, 'L');
    qr.addData(data.url);
    qr.make();
    qrDiv.innerHTML = qr.createSvgTag({ cellSize: 3, margin: 1 }); // 增加单元格尺寸，添加小边距

    const textDiv = document.createElement('div');
    textDiv.className = 'item-text';
    textDiv.textContent = data.text;

    div.appendChild(qrDiv);
    div.appendChild(textDiv);
    container.appendChild(div);
  });

  console.log(`视口尺寸: ${window.innerWidth}×${window.innerHeight}`);
  console.log(`网格布局: ${gridInfo.columns} 列 × ${gridInfo.maxRows} 行`);
  console.log(`单元格尺寸: ${Math.round((window.innerWidth - (gridInfo.columns - 1) * 1) / gridInfo.columns)}×${Math.round(gridInfo.cellHeight)}`);
  console.log(`一屏显示 ${itemCount} 个二维码`);
}

let resizeTimer;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    generateQRItems(); // generateQRItems 内部会调用 calculateGrid
  }, 150);
});

window.addEventListener('DOMContentLoaded', () => {
  generateQRItems();
});