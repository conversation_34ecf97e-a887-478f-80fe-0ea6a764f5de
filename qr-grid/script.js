function calculateGrid(){
  const c=document.querySelector('.grid-container'),
  w=c.clientWidth-4,h=c.clientHeight-4,
  minW=120,minH=150;

  // 计算基础网格
  let cols=Math.max(1,Math.floor(w/minW)),
  cellW=(w-(cols-1))/cols,
  cellH=Math.max(minH,cellW*1.1),
  rows=Math.max(1,Math.floor(h/(cellH+1))),
  actualH=(h-(rows-1))/rows;

  // 计算缩放比例 - 当单元格太小时进行缩放
  let scale=1;
  if(cellW<minW||actualH<minH){
    const scaleW=cellW/minW;
    const scaleH=actualH/minH;
    scale=Math.min(scaleW,scaleH,1); // 不放大，只缩小
  }

  c.style.gridTemplateColumns=`repeat(${cols},${cellW}px)`;
  c.style.gridAutoRows=`${actualH}px`;

  return{columns:cols,maxRows:rows,maxItems:cols*rows,cellHeight:actualH,scale:scale};
}

function getBrowserFingerprint(){
  const canvas=document.createElement('canvas');
  const ctx=canvas.getContext('2d');
  ctx.textBaseline='top';
  ctx.font='14px Arial';
  ctx.fillText('Browser fingerprint',2,2);
  const canvasData=canvas.toDataURL();

  const fingerprint=[
    navigator.userAgent,
    navigator.language,
    screen.width+'x'+screen.height,
    new Date().getTimezoneOffset(),
    canvasData.slice(-50),
    navigator.hardwareConcurrency||4,
    navigator.deviceMemory||4
  ].join('|');

  let hash=0;
  for(let i=0;i<fingerprint.length;i++){
    const char=fingerprint.charCodeAt(i);
    hash=((hash<<5)-hash)+char;
    hash=hash&hash;
  }
  return Math.abs(hash);
}

function seededRandom(seed){
  const x=Math.sin(seed)*10000;
  return x-Math.floor(x);
}

function generateRandomString(length,seed){
  const chars='0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result='';
  for(let i=0;i<length;i++){
    const randomIndex=Math.floor(seededRandom(seed+i)*chars.length);
    result+=chars.charAt(randomIndex);
  }
  return result;
}

function generateURL(barcode,timestamp,fingerprint,index){
  const prefix='HTTPS://Y2WM.CN/99.1000.1/AC20';
  const middle=barcode.substring(7,12);
  const seed=timestamp+fingerprint+index;
  const randomSuffix=String(Math.floor(seededRandom(seed)*4)).padStart(3,'0');
  const randomChars=generateRandomString(21,seed+1000);
  return prefix+middle+randomSuffix+randomChars;
}

let cigaretteData=[];
let browserFingerprint=getBrowserFingerprint();
let updateTimer;

function waitForQRCode(callback){
  if(typeof QRCode!=='undefined'&&QRCode.toDataURL){
    callback();
  }else{
    setTimeout(()=>waitForQRCode(callback),100);
  }
}

async function loadCigaretteData(){
  try{
    const response=await fetch('fujian_cigarettes_data.json');
    const data=await response.json();
    cigaretteData=data.filter(item=>item.烟型==='烤烟型');
    console.log(`加载了${cigaretteData.length}条烤烟型数据`);
    console.log(`浏览器指纹: ${browserFingerprint}`);
    waitForQRCode(startAutoUpdate);
  }catch(error){
    console.error('加载数据失败:',error);
    waitForQRCode(startAutoUpdate);
  }
}

function startAutoUpdate(){
  generateQRItems();
  updateTimer=setInterval(generateQRItems,1000);
}

function generateQRItems(){
  const c=document.querySelector('.grid-container');
  const g=calculateGrid(),n=g.maxItems;
  const timestamp=Math.floor(Date.now()/1000);

  if(c.children.length!==n){
    c.innerHTML='';
    for(let i=0;i<n;i++){
      const d=document.createElement('div'),q=document.createElement('div'),t=document.createElement('div'),u=document.createElement('div');
      d.className='grid-item';
      const wrapper = document.createElement('div');
      wrapper.className = 'qr-wrapper';
      q.className='qr-code';
      t.className='item-text';
      u.className='item-url';

      // 应用缩放
      if(g.scale<1){
        wrapper.style.transform=`scale(${g.scale})`;
        wrapper.style.transformOrigin='center center';
      }else{
        wrapper.style.transform='';
      }

      wrapper.appendChild(q);
      wrapper.appendChild(t);
      wrapper.appendChild(u);
      d.appendChild(wrapper);
      c.appendChild(d);
    }
  }else{
    // 更新现有元素的缩放
    for(let i=0;i<n;i++){
      const wrapper=c.children[i].querySelector('.qr-wrapper');
      if(g.scale<1){
        wrapper.style.transform=`scale(${g.scale})`;
        wrapper.style.transformOrigin='center center';
      }else{
        wrapper.style.transform='';
      }
    }
  }

  for(let i=0;i<n;i++){
    const d=c.children[i];
    const q=d.querySelector('.qr-code');
    const t=d.querySelector('.item-text');
    const u=d.querySelector('.item-url');

    let brandName,url;
    if(cigaretteData.length>0){
      let usedIndices = [];
      let randomIndex;
      do {
        randomIndex = Math.floor(seededRandom(timestamp+browserFingerprint+i+Date.now()) * cigaretteData.length);
      } while (usedIndices.includes(randomIndex) && usedIndices.length < cigaretteData.length);
      usedIndices.push(randomIndex);
      const item=cigaretteData[randomIndex];
      brandName=item.品牌规格名称;
      url=generateURL(item.条包条形码,timestamp,browserFingerprint,i);
    }else{
      brandName=`烤烟型 ${i+1}`;
      const seed=timestamp+browserFingerprint+i+Date.now();
      url=`HTTPS://Y2WM.CN/99.1000.1/AC20${String(i).padStart(5,'0')}${String(Math.floor(seededRandom(seed)*4)).padStart(3,'0')}${generateRandomString(21,seed)}`;
    }

    // 动态调整二维码大小
    const qrSize=Math.max(80,Math.min(150,g.cellHeight*0.6*g.scale));

    q.innerHTML = '';
    const canvas = document.createElement('canvas');
    q.appendChild(canvas);
    QRCode.toCanvas(canvas, url, {
      version: 3,
      width: qrSize,
      margin: 1
    }, function(error) {
      if(error) {
        console.error('二维码生成失败:', error);
        q.innerHTML = '<div style="width:100%;height:100px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;font-size:10px;">生成失败</div>';
      }
    });

    // 动态调整字体大小
    const fontSize=Math.max(8,Math.min(14,12*g.scale));
    const urlFontSize=Math.max(6,Math.min(10,8*g.scale));

    t.textContent=brandName;
    t.style.fontSize=`${fontSize}px`;

    u.textContent=url;
    u.style.fontSize=`${urlFontSize}px`;
  }
  console.log(`${timestamp}: ${g.columns}×${g.maxRows}=${n}个二维码已更新`);
}

let t;
addEventListener('resize',()=>{
  clearTimeout(t);
  t=setTimeout(()=>{
    clearInterval(updateTimer);
    startAutoUpdate();
  },150);
});
addEventListener('DOMContentLoaded',loadCigaretteData);
addEventListener('visibilitychange',()=>{
  if(document.hidden){
    clearInterval(updateTimer);
  }else{
    startAutoUpdate();
  }
});