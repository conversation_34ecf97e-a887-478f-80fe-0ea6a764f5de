function calculateGrid() {
  // 最小尺寸常量
  const MIN_QR_SIZE = 100; // 二维码最小可扫描尺寸
  const TEXT_HEIGHT = 15; // 文本区域高度
  const PADDING = 5; // 单元格内边距
  const GAP = 1; // 网格间隙
  const SAFETY_MARGIN = 2; // 安全边距，防止内容溢出
  
  const container = document.querySelector('.grid-container');
  
  // 获取容器实际尺寸并应用安全边距
  const containerWidth = container.clientWidth - (SAFETY_MARGIN * 2);
  const containerHeight = container.clientHeight - (SAFETY_MARGIN * 2);
  
  // 计算最小单元格尺寸（包含内边距和文本区域）
  const MIN_CELL_WIDTH = MIN_QR_SIZE + (PADDING * 2);
  const MIN_CELL_HEIGHT = MIN_QR_SIZE + TEXT_HEIGHT + (PADDING * 2);
  
  // 优化后的网格布局算法
  // 计算最大可能的列数
  const maxPossibleColumns = Math.floor(containerWidth / MIN_CELL_WIDTH);
  
  // 预计算所有可能的列数配置
  let bestConfig = {
    columns: 1,
    rows: 1,
    cellWidth: containerWidth,
    cellHeight: containerHeight,
    totalCells: 1
  };
  
  // 遍历所有可能的列数，寻找最优解
  for (let columns = maxPossibleColumns; columns >= 1; columns--) {
    const availableWidth = containerWidth - ((columns - 1) * GAP);
    const cellWidth = availableWidth / columns;
    
    // 计算单元格高度（保持宽高比例）
    const cellHeight = Math.max(MIN_CELL_HEIGHT, cellWidth * 1.1);
    
    // 计算可能的行数
    const rows = Math.floor((containerHeight - GAP) / (cellHeight + GAP));
    
    // 计算总单元格数
    const totalCells = columns * rows;
    
    // 更新最佳配置
    if (totalCells > bestConfig.totalCells || 
        (totalCells === bestConfig.totalCells && cellWidth > bestConfig.cellWidth)) {
      bestConfig = {
        columns,
        rows,
        cellWidth,
        cellHeight,
        totalCells
      };
    }
  }
  
  // 设置网格样式
  container.style.gridTemplateColumns = `repeat(${bestConfig.columns}, ${bestConfig.cellWidth}px)`;
  container.style.gridAutoRows = `${bestConfig.cellHeight}px`;
  
  // 确保网格容器居中且不溢出
  container.style.padding = '2px';
  container.style.boxSizing = 'border-box';
  
  // 返回网格信息
  return { 
    columns: bestConfig.columns, 
    maxRows: bestConfig.rows, 
    maxItems: bestConfig.totalCells, 
    cellWidth: bestConfig.cellWidth,
    cellHeight: bestConfig.cellHeight, 
    containerWidth, 
    containerHeight 
  };
}

async function generateQRItems() {
  const container = document.querySelector('.grid-container');
  container.innerHTML = '';

  // 计算网格布局
  const gridInfo = calculateGrid();

  // 从JSON数据中筛选烤烟型产品
  try {
    const response = await fetch('fujian_cigarettes_data.json');
    if (!response.ok) throw new Error('Failed to fetch data');
    const data = await response.json();
    const filteredData = data.filter(item => item.烟型 === "烤烟型");
    if (filteredData.length === 0) throw new Error('No烤烟型数据 found');
   
   // 生成URL和显示数据
  } catch (error) {
    console.error('Error loading data:', error);
    // 回退到默认演示数据
    const itemCount = gridInfo.maxItems;
    const demoData = Array(itemCount).fill().map((_,i) => ({
      text: `Demo QR ${i+1}`,
      url: `https://example.com/qr-${i+1}`
    }));
  const demoData = filteredData.slice(0, gridInfo.maxItems).map(item => {
    const barcodePart = item.条包条形码.substring(7, 12);
    const randomSuffix = Math.floor(Math.random() * 4).toString().padStart(3, '0');
    const randomChars = Array.from({length: 21}, () => {
      const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      return chars.charAt(Math.floor(Math.random() * chars.length));
    }).join('');
    
    const url = `HTTPS://Y2WM.CN/99.1000.1/AC20${barcodePart}${randomSuffix}${randomChars}`;
    
    return {
      text: item.品牌规格名称,
      url: url
    };
  });
  
  // 计算最佳二维码尺寸
  // 单元格宽度的80%作为二维码的最大宽度
  const qrSize = Math.floor(Math.min(gridInfo.cellWidth * 0.85, gridInfo.cellHeight * 0.75));
  // 根据二维码尺寸计算最佳的cellSize参数
  const cellSize = Math.max(2, Math.floor(qrSize / 33)); // 假设QR码是33×33的矩阵

  demoData.forEach(data => {
    const div = document.createElement('div');
    div.className = 'grid-item';

    const qrDiv = document.createElement('div');
    qrDiv.className = 'qr-code';

    const qr = qrcode(0, 'L');
    qr.addData(data.url);
    qr.make();
    qrDiv.innerHTML = qr.createSvgTag({ 
      cellSize: cellSize, 
      margin: 1,
      scalable: true // 使SVG可缩放
    });

    const textDiv = document.createElement('div');
    textDiv.className = 'item-text';
    textDiv.textContent = data.text;

    div.appendChild(qrDiv);
    div.appendChild(textDiv);
    container.appendChild(div);
  });

  // 输出详细的网格信息
  console.log(`%c二维码网格布局信息`, 'font-weight:bold;color:#0066cc;');
  console.log(`视口尺寸: ${window.innerWidth}×${window.innerHeight}px`);
  console.log(`容器尺寸: ${Math.round(gridInfo.containerWidth)}×${Math.round(gridInfo.containerHeight)}px`);
  console.log(`网格布局: ${gridInfo.columns}列 × ${gridInfo.maxRows}行 = ${itemCount}个二维码`);
  console.log(`单元格尺寸: ${Math.round(gridInfo.cellWidth)}×${Math.round(gridInfo.cellHeight)}px`);
  console.log(`二维码尺寸: 约${Math.round(qrSize)}px (cellSize=${cellSize})`);
  console.log(`空间利用率: ${Math.round((itemCount * gridInfo.cellWidth * gridInfo.cellHeight) / (gridInfo.containerWidth * gridInfo.containerHeight) * 100)}%`);
}

// 优化窗口调整事件处理
let resizeTimer;
let lastWidth = window.innerWidth;
let lastHeight = window.innerHeight;

window.addEventListener('resize', () => {
  clearTimeout(resizeTimer);
  
  // 只有当尺寸变化超过阈值时才重新计算
  const widthDiff = Math.abs(window.innerWidth - lastWidth);
  const heightDiff = Math.abs(window.innerHeight - lastHeight);
  
  if (widthDiff > 10 || heightDiff > 10) { // 10px的阈值
    resizeTimer = setTimeout(() => {
      lastWidth = window.innerWidth;
      lastHeight = window.innerHeight;
      generateQRItems(); // 重新计算并生成网格
      
      // 在控制台显示窗口调整信息
      console.log(`%c窗口已调整: ${lastWidth}×${lastHeight}px`, 'color:#009900;');
    }, 100); // 减少延迟时间提高响应速度
  }
});

window.addEventListener('DOMContentLoaded', () => {
  generateQRItems();
});