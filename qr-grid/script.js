function calculateGrid() {
  const MIN_CELL_WIDTH = 180;

  const container = document.querySelector('.grid-container');
  const viewportWidth = window.innerWidth - 10;

  const columns = Math.max(1, Math.floor(viewportWidth / MIN_CELL_WIDTH));
  const cellWidth = (viewportWidth - (columns + 1) * 4) / columns;

  container.style.gridTemplateColumns = `repeat(${columns}, ${cellWidth}px)`;
  container.style.gridAutoRows = `${cellWidth * 1.1}px`;

  return { columns };
}

function generateQRItems() {
  const container = document.querySelector('.grid-container');
  container.innerHTML = '';

  const demoData = Array(24).fill().map((_,i) => ({
    text: `QR ${i+1}`,
    url: `https://example.com/qr-${i+1}`
  }));

  demoData.forEach(data => {
    const div = document.createElement('div');
    div.className = 'grid-item';

    const qrDiv = document.createElement('div');
    qrDiv.className = 'qr-code';

    const qr = qrcode(0, 'M');
    qr.addData(data.url);
    qr.make();
    qrDiv.innerHTML = qr.createSvgTag({ cellSize: 3, margin: 1 });

    const textDiv = document.createElement('div');
    textDiv.className = 'item-text';
    textDiv.textContent = data.text;

    div.appendChild(qrDiv);
    div.appendChild(textDiv);
    container.appendChild(div);
  });
}

let resizeTimer;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    calculateGrid();
    generateQRItems();
  }, 150);
});

window.addEventListener('DOMContentLoaded', () => {
  calculateGrid();
  generateQRItems();
});