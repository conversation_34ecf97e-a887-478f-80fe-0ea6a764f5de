function calculateGrid() {
  const MIN_CELL_WIDTH = 220; // 减小最小单元格宽度以容纳更多列
const MAX_VISIBLE_HEIGHT = window.innerHeight - 40;
const MIN_CELL_HEIGHT = 200; // 减小最小单元格高度以容纳更多行
  
  const container = document.querySelector('.grid-container');
  const viewportWidth = container.clientWidth;
  const viewportHeight = window.innerHeight;

  // 计算最大可容纳列数
  const columns = Math.max(1, Math.floor(viewportWidth / MIN_CELL_WIDTH));
  // 计算实际单元格宽度
  const cellWidth = (viewportWidth - 30) / columns; // 30px为总间隙

  // 计算行数（考虑导航栏高度）
  const rows = Math.min(
  Math.max(1, Math.floor(MAX_VISIBLE_HEIGHT / MIN_CELL_HEIGHT)),
  Math.floor((viewportHeight - 40) / MIN_CELL_HEIGHT)
);

  // 设置网格布局
  container.style.gridTemplateColumns = `repeat(${columns}, ${cellWidth}px)`;
  container.style.gridAutoRows = `${cellWidth * 1.2}px`; // 按宽高比设置行高

  return { columns, rows };
}

function generateQRItems() {
  const container = document.querySelector('.grid-container');
  container.innerHTML = '';

  // 示例数据 - 实际使用时替换为动态数据
  const demoData = Array(20).fill().map((_,i) => ({
    text: `二维码 ${i+1}`,
    url: `https://example.com/qr-${i+1}`
  }));

  demoData.forEach(data => {
    const div = document.createElement('div');
    div.className = 'grid-item';
    
    // 创建二维码容器
    const qrDiv = document.createElement('div');
    qrDiv.className = 'qr-code';
    
    // 生成二维码
    const qr = qrcode(0, 'M');
    qr.addData(data.url);
    qr.make();
    qrDiv.innerHTML = qr.createSvgTag({ cellSize: 4, margin: 2 });

    // 创建文本容器
    const textDiv = document.createElement('div');
    textDiv.className = 'item-text';
    textDiv.textContent = data.text;

    div.appendChild(qrDiv);
    div.appendChild(textDiv);
    container.appendChild(div);
  });
}

// 自适应调整
let resizeTimer;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    calculateGrid();
    generateQRItems();
  }, 200);
});

// 初始化
window.addEventListener('DOMContentLoaded', () => {
  calculateGrid();
  generateQRItems();
});